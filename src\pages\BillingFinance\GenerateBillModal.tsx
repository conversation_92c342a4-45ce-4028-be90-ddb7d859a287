import React, { useState, useEffect, useRef } from "react";
import {
  Modal,
  Form,
  DatePicker,
  Table,
  Button,
  Space,
  message,
  Steps,
  Card,
  Typography,
  Select,
  Row,
  Col,
  Input,
  Spin,
  Tag,
  Collapse,
  Statistic,
  Result,
  Checkbox,
  Progress,
  Alert,
  InputNumber,
} from "antd";
import {
  UserOutlined,
  ToolOutlined,
  CheckCircleOutlined,
  InfoCircleOutlined,
  CalendarOutlined,
  FileTextOutlined,
  SettingOutlined,
  PlayCircleOutlined,
  ClockCircleOutlined,
  CheckOutlined,
  CloseOutlined,
  SearchOutlined,
} from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table";
import dayjs from "dayjs";
import {
  fetchBillingUsers,
  fetchUserShippingTemplates,
  asyncGenerateBilling,
  fetchBillingTaskDetail,
  BillingUserInfo,
  ShippingFeeTemplate,
  ShippingTemplateType,
  AsyncGenerateBillingRequest,
  UserTemplateConfig,
  BillingTaskData,
  BillingTaskDetail,
  BillingTaskStatus,
} from "../../services/billingService";
import { BillingCycle } from "../../services/billingCycleService";
import {
  getActiveShipmentTypes,
  getShippingFeeTemplates,
  getUserTemplateConfigurations,
  getTemplateTypeFromShipmentType,
  ShipmentType,
  UserTemplateConfigItem,
} from "../../services/shippingFeeService";

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Panel } = Collapse;

interface GenerateBillModalProps {
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
  billingCycleId?: number; // 可选的账期批次ID
  billingCycle?: BillingCycle; // 添加账期信息参数
}

// 步骤枚举
enum GenerateStep {
  SELECT_TIME = 0, // 选择时间
  SELECT_USERS = 1, // 选择用户
  CONFIGURE_TEMPLATES = 2, // 配置模板
  CONFIRM = 3, // 确认生成
  TASK_MONITORING = 4, // 任务监控
}

// 用户模板状态接口
interface UserTemplateState {
  userId: number;
  nickname: string;
  username: string; // 添加用户名字段
  originalTemplates: ShippingFeeTemplate[];
  useSystemDefault: boolean;
  templateLoaded: boolean; // 是否已加载模板
  loading: boolean; // 是否正在加载模板
  customTemplates?: {
    generalTemplate?: ShippingFeeTemplate;
    batteryTemplate?: ShippingFeeTemplate;
    postBoxTemplate?: ShippingFeeTemplate;
  };
}

const GenerateBillModal: React.FC<GenerateBillModalProps> = ({
  visible,
  onClose,
  onSuccess,
  billingCycleId,
  billingCycle,
}) => {
  const [form] = Form.useForm();
  const [templateForm] = Form.useForm();
  const intervalRef = useRef<number | null>(null);

  // 当前步骤
  const [currentStep, setCurrentStep] = useState<GenerateStep>(
    GenerateStep.SELECT_TIME
  );

  // 加载状态
  const [loading, setLoading] = useState(false);

  // 时间范围
  const [timeRange, setTimeRange] = useState<[string, string] | null>(null);

  // 用户相关状态
  const [userList, setUserList] = useState<BillingUserInfo[]>([]);
  const [selectedUserIds, setSelectedUserIds] = useState<number[]>([]);
  const [userSearchText, setUserSearchText] = useState<string>(""); // 用户检索关键词

  // 模板配置状态
  const [userTemplatesState, setUserTemplatesState] = useState<
    UserTemplateState[]
  >([]);

  // 货物类型状态
  const [shipmentTypes, setShipmentTypes] = useState<ShipmentType[]>([]);
  const [allTemplates, setAllTemplates] = useState<ShippingFeeTemplate[]>([]);

  // 客户检索状态
  const [customerSearchText, setCustomerSearchText] = useState<string>(""); // 检索关键词

  // 任务状态
  const [currentTask, setCurrentTask] = useState<BillingTaskData | null>(null);
  const [taskDetail, setTaskDetail] = useState<BillingTaskDetail | null>(null);
  const [taskMonitoring, setTaskMonitoring] = useState(false);

  /**
   * 步骤配置
   */
  const steps = [
    {
      title: "选择时间",
      icon: <CalendarOutlined />,
      description: "选择账单生成的时间范围",
    },
    {
      title: "选择用户",
      icon: <UserOutlined />,
      description: "选择需要生成账单的用户",
    },
    {
      title: "配置模板",
      icon: <ToolOutlined />,
      description: "配置运费模板参数",
    },
    {
      title: "确认生成",
      icon: <CheckCircleOutlined />,
      description: "确认参数并生成账单",
    },
    {
      title: "任务监控",
      icon: <ClockCircleOutlined />,
      description: "监控生成任务进度",
    },
  ];

  /**
   * 重置状态
   */
  const resetState = () => {
    setCurrentStep(GenerateStep.SELECT_TIME);
    setTimeRange(null);
    setUserList([]);
    setSelectedUserIds([]);
    setUserSearchText(""); // 清理用户检索状态
    setUserTemplatesState([]);
    setShipmentTypes([]); // 清理货物类型状态
    setAllTemplates([]); // 清理模板状态
    setCustomerSearchText(""); // 清理客户检索状态
    setCurrentTask(null);
    setTaskDetail(null);
    setTaskMonitoring(false);
    form.resetFields();
    templateForm.resetFields();

    // 清理定时器
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  };

  /**
   * 获取货物类型列表
   */
  const fetchShipmentTypes = async () => {
    try {
      const types = await getActiveShipmentTypes();
      setShipmentTypes(types);
    } catch (error) {
      console.error("获取货物类型失败:", error);
      message.error("获取货物类型失败，请重试");
    }
  };

  /**
   * 获取所有运费模板
   */
  const fetchAllTemplates = async () => {
    try {
      // 获取所有模板，不提供搜索条件则返回所有模板
      const response = await getShippingFeeTemplates();
      setAllTemplates(response.templates);
    } catch (error) {
      console.error("获取运费模板失败:", error);
      message.error("获取运费模板失败，请重试");
    }
  };

  /**
   * 处理第一步：时间选择
   */
  const handleTimeSelection = async () => {
    try {
      const values = await form.validateFields();
      const [startTime, endTime] = values.timeRange;

      const timeStart = startTime.format("YYYY-MM-DD HH:mm:ss");
      const timeEnd = endTime.format("YYYY-MM-DD HH:mm:ss");
      setTimeRange([timeStart, timeEnd]);

      setLoading(true);

      // 获取货物类型和模板数据
      await Promise.all([fetchShipmentTypes(), fetchAllTemplates()]);

      // 查询可生成账单的用户
      const usersData = await fetchBillingUsers(timeStart, timeEnd);
      setUserList(usersData.list);

      // 默认全选用户
      const allUserIds = usersData.list.map((user) => user.userId);
      setSelectedUserIds(allUserIds);

      setCurrentStep(GenerateStep.SELECT_USERS);
    } catch (error) {
      console.error("查询可生成账单用户失败:", error);
      message.error("查询可生成账单用户失败，请重试");
    } finally {
      setLoading(false);
    }
  };

  /**
   * 处理第二步：用户选择
   */
  const handleUserSelection = async () => {
    if (selectedUserIds.length === 0) {
      message.warning("请至少选择一个用户");
      return;
    }

    // 初始化用户模板状态，不加载模板数据
    const templateStates: UserTemplateState[] = selectedUserIds.map(
      (userId) => {
        const user = userList.find((u) => u.userId === userId);
        if (!user) {
          throw new Error(`用户 ${userId} 信息未找到`);
        }

        return {
          userId,
          nickname: user.nickname,
          username: user.username,
          originalTemplates: [],
          useSystemDefault: true, // 默认使用系统配置
          templateLoaded: false, // 未加载模板
          loading: false, // 不在加载中
        };
      }
    );

    setUserTemplatesState(templateStates);
    setCurrentStep(GenerateStep.CONFIGURE_TEMPLATES);
  };

  /**
   * 处理第三步：模板配置
   */
  const handleTemplateConfiguration = async () => {
    try {
      // 验证基本表单
      await templateForm.validateFields();
      setCurrentStep(GenerateStep.CONFIRM);
    } catch (error) {
      console.error("模板配置验证失败:", error);
      message.error("请检查模板配置");
    }
  };

  /**
   * 处理第四步：确认生成
   */
  const handleConfirmGeneration = async () => {
    try {
      setLoading(true);

      // 获取基本表单数据
      const formValues = await templateForm.validateFields();

      // 构建生成参数
      const params: AsyncGenerateBillingRequest = {
        startTime: timeRange![0],
        endTime: timeRange![1],
        customerIds: selectedUserIds,
        billingCycleId,
        dueDate: formValues.dueDate?.format("YYYY-MM-DD"),
        currency: formValues.currency || "CNY",
        notes: formValues.notes,
      };

      // 构建用户自定义模板配置 - 所有使用自定义配置的用户都传给后端
      const userTemplates: UserTemplateConfig[] = [];

      for (const userState of userTemplatesState) {
        if (!userState.useSystemDefault) {
          // 使用原始模板作为自定义模板的基础，如果有修改的customTemplates则使用修改后的
          const templateConfig: UserTemplateConfig = {
            userId: userState.userId,
          };

          // 查找各类型的模板
          const generalTemplate =
            userState.customTemplates?.generalTemplate ||
            userState.originalTemplates.find(
              (t) => t.type === ShippingTemplateType.GENERAL
            );
          const batteryTemplate =
            userState.customTemplates?.batteryTemplate ||
            userState.originalTemplates.find(
              (t) => t.type === ShippingTemplateType.BATTERY
            );
          const postBoxTemplate =
            userState.customTemplates?.postBoxTemplate ||
            userState.originalTemplates.find(
              (t) => t.type === ShippingTemplateType.POST_BOX
            );

          if (generalTemplate) templateConfig.generalTemplate = generalTemplate;
          if (batteryTemplate) templateConfig.batteryTemplate = batteryTemplate;
          if (postBoxTemplate) templateConfig.postBoxTemplate = postBoxTemplate;

          userTemplates.push(templateConfig);
        }
      }

      if (userTemplates.length > 0) {
        params.userTemplates = userTemplates;
      }

      // 调用异步生成接口
      const taskData = await asyncGenerateBilling(params);
      setCurrentTask(taskData);
      setCurrentStep(GenerateStep.TASK_MONITORING);

      // 开始监控任务
      startTaskMonitoring(taskData.taskId);

      message.success("账单生成任务已创建，正在处理中...");
    } catch (error) {
      console.error("创建账单生成任务失败:", error);
      message.error("创建账单生成任务失败，请重试");
    } finally {
      setLoading(false);
    }
  };

  /**
   * 开始任务监控
   */
  const startTaskMonitoring = (taskId: string) => {
    setTaskMonitoring(true);

    // 立即查询一次
    fetchTaskDetail(taskId);

    // 设置定时查询
    intervalRef.current = window.setInterval(() => {
      fetchTaskDetail(taskId);
    }, 3000); // 每3秒查询一次
  };

  /**
   * 查询任务详情
   */
  const fetchTaskDetail = async (taskId: string) => {
    try {
      const detail = await fetchBillingTaskDetail(taskId);
      setTaskDetail(detail);

      // 如果任务已完成或失败，停止监控
      if (detail.isCompleted || detail.isFailed) {
        setTaskMonitoring(false);
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
          intervalRef.current = null;
        }

        if (detail.isCompleted) {
          message.success("账单生成任务已完成！");
          // 调用成功回调来刷新账单列表和账单批次数据
          onSuccess();
        } else if (detail.isFailed) {
          message.error(
            `账单生成任务失败：${detail.errorMessage || "未知错误"}`
          );
        }
      }
    } catch (error) {
      console.error("查询任务详情失败:", error);
      // 不显示错误消息，避免干扰用户
    }
  };

  /**
   * 加载用户模板 - 使用与UserTemplateConfigPage相同的逻辑
   */
  const loadUserTemplates = async (userId: number) => {
    setUserTemplatesState((prev) =>
      prev.map((state) =>
        state.userId === userId ? { ...state, loading: true } : state
      )
    );

    try {
      // 使用新的接口获取用户模板配置
      const response = await getUserTemplateConfigurations(userId);

      // 创建货物类型到模板的映射关系
      const shipmentTypeToTemplateMap: Record<number, number> = {};

      // 遍历用户配置的模板，根据模板的type字段建立与货物类型的映射
      response.templates.forEach((template: UserTemplateConfigItem) => {
        // 查找模板类型对应的货物类型
        const matchedShipmentType = shipmentTypes.find((shipmentType) => {
          const expectedTemplateType =
            getTemplateTypeFromShipmentType(shipmentType);
          return expectedTemplateType === template.type;
        });

        if (matchedShipmentType) {
          shipmentTypeToTemplateMap[matchedShipmentType.id] = template.id;
        }
      });

      // 根据映射关系构建模板数组
      const userTemplates: ShippingFeeTemplate[] = [];
      shipmentTypes.forEach((shipmentType) => {
        const templateId = shipmentTypeToTemplateMap[shipmentType.id];
        if (templateId) {
          // 从所有模板中查找对应的模板
          const template = allTemplates.find((t) => t.id === templateId);
          if (template) {
            // 添加类型信息以便后续使用
            const templateWithType = {
              ...template,
              type: getTemplateTypeFromShipmentType(shipmentType),
              typeName: shipmentType.name,
            };
            userTemplates.push(templateWithType);
          }
        }
      });

      setUserTemplatesState((prev) =>
        prev.map((state) =>
          state.userId === userId
            ? {
                ...state,
                originalTemplates: userTemplates,
                templateLoaded: true,
                loading: false,
              }
            : state
        )
      );
    } catch (error) {
      console.error(`加载用户 ${userId} 模板失败:`, error);
      message.error(`加载用户模板失败，请重试`);

      // 重置为系统默认
      setUserTemplatesState((prev) =>
        prev.map((state) =>
          state.userId === userId
            ? {
                ...state,
                useSystemDefault: true,
                loading: false,
              }
            : state
        )
      );
    }
  };

  /**
   * 切换用户的模板配置模式
   */
  const toggleUserTemplateMode = async (
    userId: number,
    useSystemDefault: boolean
  ) => {
    // 如果切换到自定义配置且模板未加载，先加载模板
    if (!useSystemDefault) {
      const userState = userTemplatesState.find((s) => s.userId === userId);
      if (userState && !userState.templateLoaded) {
        await loadUserTemplates(userId);
      }
    }

    setUserTemplatesState((prev) =>
      prev.map((state) =>
        state.userId === userId ? { ...state, useSystemDefault } : state
      )
    );
  };

  /**
   * 更新用户自定义模板
   */
  const updateUserCustomTemplate = (
    userId: number,
    templateType: "generalTemplate" | "batteryTemplate" | "postBoxTemplate",
    template: ShippingFeeTemplate
  ) => {
    setUserTemplatesState((prev) =>
      prev.map((state) =>
        state.userId === userId
          ? {
              ...state,
              customTemplates: {
                ...state.customTemplates,
                [templateType]: template,
              },
            }
          : state
      )
    );
  };

  /**
   * 重新选择时间
   */
  const handleReselectTime = () => {
    setCurrentStep(GenerateStep.SELECT_TIME);
    setUserList([]);
    setSelectedUserIds([]);
    setUserTemplatesState([]);
  };

  /**
   * 重新选择用户
   */
  const handleReselectUsers = () => {
    setCurrentStep(GenerateStep.SELECT_USERS);
    setUserTemplatesState([]);
  };

  /**
   * 重新配置模板
   */
  const handleReconfigureTemplates = () => {
    setCurrentStep(GenerateStep.CONFIGURE_TEMPLATES);
  };

  /**
   * 用户列表表格列定义
   */
  const userColumns: ColumnsType<BillingUserInfo> = [
    {
      title: "选择",
      key: "select",
      width: 60,
      render: (_, record) => (
        <Checkbox
          checked={selectedUserIds.includes(record.userId)}
          onChange={(e) => {
            if (e.target.checked) {
              setSelectedUserIds((prev) => [...prev, record.userId]);
            } else {
              setSelectedUserIds((prev) =>
                prev.filter((id) => id !== record.userId)
              );
            }
          }}
        />
      ),
    },
    {
      title: "用户信息",
      key: "user",
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: "bold" }}>{record.nickname}</div>
          <div style={{ fontSize: "12px", color: "#999" }}>
            ID: {record.userId} | 用户名: {record.username}
          </div>
        </div>
      ),
    },
    {
      title: "运单数量",
      dataIndex: "manifestCount",
      align: "center",
      render: (count: number, record) => (
        <Statistic
          value={count}
          suffix="单"
          valueStyle={{
            fontSize: "14px",
            color: record.hasManifests ? "#1890ff" : "#999",
          }}
        />
      ),
    },
    {
      title: "调整记录",
      dataIndex: "adjustmentCount",
      align: "center",
      render: (count: number, record) => (
        <Statistic
          value={count}
          suffix="条"
          valueStyle={{
            fontSize: "14px",
            color: record.hasAdjustments ? "#52c41a" : "#999",
          }}
        />
      ),
    },
    {
      title: "总计",
      dataIndex: "totalCount",
      align: "center",
      render: (count: number) => (
        <Statistic
          value={count}
          valueStyle={{ fontSize: "14px", fontWeight: "bold" }}
        />
      ),
    },
  ];

  /**
   * 渲染模板配置表单
   */
  const renderTemplateConfigForm = (
    userState: UserTemplateState,
    templateType: "generalTemplate" | "batteryTemplate" | "postBoxTemplate",
    title: string
  ) => {
    const originalTemplate = userState.originalTemplates.find(
      (t) =>
        (templateType === "generalTemplate" &&
          t.type === ShippingTemplateType.GENERAL) ||
        (templateType === "batteryTemplate" &&
          t.type === ShippingTemplateType.BATTERY) ||
        (templateType === "postBoxTemplate" &&
          t.type === ShippingTemplateType.POST_BOX)
    );

    // 创建默认模板结构，用于没有原始模板的情况
    const defaultTemplate: ShippingFeeTemplate = {
      id: 0,
      name: `默认${title}`,
      type:
        templateType === "generalTemplate"
          ? ShippingTemplateType.GENERAL
          : templateType === "batteryTemplate"
          ? ShippingTemplateType.BATTERY
          : ShippingTemplateType.POST_BOX,
      typeName: title,
      firstWeightPrice: 0,
      firstWeightRange: 0.5,
      continuedWeightPrice: 0,
      continuedWeightInterval: 0.5,
      bulkCoefficient: 5000,
      threeSidesStart: 60,
    };

    const customTemplate = userState.customTemplates?.[templateType];
    const currentTemplate =
      customTemplate || originalTemplate || defaultTemplate;

    // 确定面板标题
    const panelTitle = originalTemplate
      ? `${title} - ${originalTemplate.name}`
      : `${title} - 新建配置`;

    return (
      <Panel header={panelTitle} key={templateType}>
        {!originalTemplate && (
          <Alert
            message="该用户暂无此类型模板，您可以从零开始配置"
            type="info"
            showIcon
            style={{ marginBottom: "16px" }}
          />
        )}
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="首重价格（元）">
              <InputNumber
                value={currentTemplate.firstWeightPrice}
                min={0}
                precision={2}
                style={{ width: "100%" }}
                placeholder="请输入首重价格"
                disabled={userState.useSystemDefault}
                onChange={(value) => {
                  if (!userState.useSystemDefault && value !== null) {
                    updateUserCustomTemplate(userState.userId, templateType, {
                      ...currentTemplate,
                      firstWeightPrice: value,
                    });
                  }
                }}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="首重范围（公斤）">
              <InputNumber
                value={currentTemplate.firstWeightRange}
                min={0}
                precision={2}
                style={{ width: "100%" }}
                placeholder="请输入首重范围"
                disabled={userState.useSystemDefault}
                onChange={(value) => {
                  if (!userState.useSystemDefault && value !== null) {
                    updateUserCustomTemplate(userState.userId, templateType, {
                      ...currentTemplate,
                      firstWeightRange: value,
                    });
                  }
                }}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="续重价格（元）">
              <InputNumber
                value={currentTemplate.continuedWeightPrice}
                min={0}
                precision={2}
                style={{ width: "100%" }}
                placeholder="请输入续重价格"
                disabled={userState.useSystemDefault}
                onChange={(value) => {
                  if (!userState.useSystemDefault && value !== null) {
                    updateUserCustomTemplate(userState.userId, templateType, {
                      ...currentTemplate,
                      continuedWeightPrice: value,
                    });
                  }
                }}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="续重区间（公斤）">
              <InputNumber
                value={currentTemplate.continuedWeightInterval}
                min={0}
                precision={2}
                style={{ width: "100%" }}
                placeholder="请输入续重区间"
                disabled={userState.useSystemDefault}
                onChange={(value) => {
                  if (!userState.useSystemDefault && value !== null) {
                    updateUserCustomTemplate(userState.userId, templateType, {
                      ...currentTemplate,
                      continuedWeightInterval: value,
                    });
                  }
                }}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="轻抛系数">
              <InputNumber
                value={currentTemplate.bulkCoefficient}
                min={1}
                style={{ width: "100%" }}
                placeholder="请输入轻抛系数"
                disabled={userState.useSystemDefault}
                onChange={(value) => {
                  if (!userState.useSystemDefault && value !== null) {
                    updateUserCustomTemplate(userState.userId, templateType, {
                      ...currentTemplate,
                      bulkCoefficient: value,
                    });
                  }
                }}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="三边和阈值（厘米）">
              <InputNumber
                value={currentTemplate.threeSidesStart}
                min={0}
                precision={1}
                style={{ width: "100%" }}
                placeholder="请输入三边和阈值"
                disabled={userState.useSystemDefault}
                onChange={(value) => {
                  if (!userState.useSystemDefault && value !== null) {
                    updateUserCustomTemplate(userState.userId, templateType, {
                      ...currentTemplate,
                      threeSidesStart: value,
                    });
                  }
                }}
              />
            </Form.Item>
          </Col>
        </Row>
      </Panel>
    );
  };

  /**
   * 渲染任务监控界面
   */
  const renderTaskMonitoring = () => {
    if (!currentTask || !taskDetail) {
      return (
        <Card>
          <Spin size="large">
            <div style={{ padding: "40px", textAlign: "center" }}>
              <Text>正在初始化任务监控...</Text>
            </div>
          </Spin>
        </Card>
      );
    }

    const getStatusColor = (status: BillingTaskStatus) => {
      switch (status) {
        case BillingTaskStatus.PENDING:
          return "orange";
        case BillingTaskStatus.PROCESSING:
          return "blue";
        case BillingTaskStatus.COMPLETED:
          return "green";
        case BillingTaskStatus.FAILED:
          return "red";
        default:
          return "default";
      }
    };

    const getStatusIcon = (status: BillingTaskStatus) => {
      switch (status) {
        case BillingTaskStatus.PENDING:
          return <ClockCircleOutlined />;
        case BillingTaskStatus.PROCESSING:
          return <PlayCircleOutlined />;
        case BillingTaskStatus.COMPLETED:
          return <CheckOutlined />;
        case BillingTaskStatus.FAILED:
          return <CloseOutlined />;
        default:
          return <InfoCircleOutlined />;
      }
    };

    if (taskDetail.isCompleted) {
      return (
        <Result
          status="success"
          title="账单生成任务已完成！"
          subTitle={taskDetail.progressDescription}
          extra={[
            <Button
              type="primary"
              key="close"
              onClick={() => {
                onSuccess(); // 确保数据被刷新
                onClose();
              }}
            >
              关闭窗口
            </Button>,
            <Button key="continue" onClick={resetState}>
              继续生成
            </Button>,
          ]}
        >
          <div
            style={{
              background: "#fafafa",
              padding: "16px",
              borderRadius: "6px",
            }}
          >
            <Row gutter={16}>
              <Col span={8}>
                <Statistic
                  title="处理客户数"
                  value={taskDetail.customerCount}
                  suffix="个"
                  prefix={<UserOutlined />}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="处理项目数"
                  value={taskDetail.itemsProcessedCount}
                  suffix="项"
                  prefix={<FileTextOutlined />}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="总耗时"
                  value={taskDetail.duration || 0}
                  suffix="秒"
                  prefix={<ClockCircleOutlined />}
                />
              </Col>
            </Row>
          </div>
        </Result>
      );
    }

    if (taskDetail.isFailed) {
      return (
        <Result
          status="error"
          title="账单生成任务失败"
          subTitle={taskDetail.errorMessage || "未知错误"}
          extra={[
            <Button key="retry" onClick={handleConfirmGeneration}>
              重新尝试
            </Button>,
            <Button key="back" onClick={handleReconfigureTemplates}>
              返回配置
            </Button>,
            <Button key="close" onClick={onClose}>
              关闭窗口
            </Button>,
          ]}
        />
      );
    }

    return (
      <Card title="任务执行监控">
        <Space direction="vertical" size="large" style={{ width: "100%" }}>
          {/* 任务基本信息 */}
          <Row gutter={16}>
            <Col span={12}>
              <Text strong>任务ID：</Text>
              <Text code>{taskDetail.taskId}</Text>
            </Col>
            <Col span={12}>
              <Text strong>任务状态：</Text>
              <Tag
                color={getStatusColor(taskDetail.status)}
                icon={getStatusIcon(taskDetail.status)}
              >
                {taskDetail.statusName}
              </Tag>
            </Col>
            <Col span={12}>
              <Text strong>提交时间：</Text>
              <Text>{taskDetail.submitTime}</Text>
            </Col>
            <Col span={12}>
              <Text strong>客户数量：</Text>
              <Text>{taskDetail.customerCount} 个</Text>
            </Col>
          </Row>

          {/* 进度条 */}
          <div>
            <Text strong>执行进度：</Text>
            <Progress
              percent={taskDetail.progressPercentage}
              status={
                taskDetail.isFailed
                  ? "exception"
                  : taskDetail.isCompleted
                  ? "success"
                  : "active"
              }
              showInfo
            />
            <Text
              type="secondary"
              style={{ marginTop: "8px", display: "block" }}
            >
              {taskDetail.progressDescription}
            </Text>
          </div>

          {/* 处理详情 */}
          {(taskDetail.isProcessing || taskDetail.isCompleted) && (
            <Row gutter={16}>
              <Col span={8}>
                <Statistic
                  title="已处理项目"
                  value={taskDetail.itemsProcessedCount}
                  suffix={`/ ${taskDetail.totalItemsToProcess}`}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="处理进度"
                  value={taskDetail.progressPercentage}
                  suffix="%"
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="已用时间"
                  value={taskDetail.duration || 0}
                  suffix="秒"
                />
              </Col>
            </Row>
          )}

          {/* 实时状态提示 */}
          {taskMonitoring && (
            <Alert
              message="正在实时监控任务状态"
              description="页面将每3秒自动刷新任务进度，请保持窗口打开直到任务完成"
              type="info"
              showIcon
              icon={<ClockCircleOutlined />}
            />
          )}
        </Space>
      </Card>
    );
  };

  /**
   * 渲染步骤内容
   */
  const renderStepContent = () => {
    switch (currentStep) {
      case GenerateStep.SELECT_TIME: {
        // 生成时间范围预设选项
        const generateTimePresets = () => {
          const presets: Array<{
            label: string;
            value: [dayjs.Dayjs, dayjs.Dayjs];
          }> = [
            {
              label: "今天",
              value: [dayjs().startOf("day"), dayjs().endOf("day")],
            },
            {
              label: "最近7天",
              value: [
                dayjs().subtract(7, "day").startOf("day"),
                dayjs().endOf("day"),
              ],
            },
            {
              label: "最近15天",
              value: [
                dayjs().subtract(15, "day").startOf("day"),
                dayjs().endOf("day"),
              ],
            },
            {
              label: "最近30天",
              value: [
                dayjs().subtract(30, "day").startOf("day"),
                dayjs().endOf("day"),
              ],
            },
            {
              label: "本月",
              value: [dayjs().startOf("month"), dayjs().endOf("month")],
            },
            {
              label: "上个月",
              value: [
                dayjs().subtract(1, "month").startOf("month"),
                dayjs().subtract(1, "month").endOf("month"),
              ],
            },
          ];

          // 如果有账期信息，添加当前账期月份的预设选项
          if (billingCycle) {
            const cycleStart = dayjs()
              .year(billingCycle.cycleYear)
              .month(billingCycle.cycleMonth - 1)
              .startOf("month");
            const cycleEnd = dayjs()
              .year(billingCycle.cycleYear)
              .month(billingCycle.cycleMonth - 1)
              .endOf("month");

            presets.unshift({
              label: `当前账期(${billingCycle.cycleYear}年${billingCycle.cycleMonth}月)`,
              value: [cycleStart, cycleEnd],
            });
          }

          return presets;
        };

        // 设置默认时间范围
        const getDefaultTimeRange = (): [dayjs.Dayjs, dayjs.Dayjs] => {
          if (billingCycle) {
            // 如果有账期信息，默认选择该月份
            return [
              dayjs()
                .year(billingCycle.cycleYear)
                .month(billingCycle.cycleMonth - 1)
                .startOf("month"),
              dayjs()
                .year(billingCycle.cycleYear)
                .month(billingCycle.cycleMonth - 1)
                .endOf("month"),
            ];
          }
          // 否则默认选择本月
          return [dayjs().startOf("month"), dayjs().endOf("month")];
        };

        return (
          <Card>
            <Form
              form={form}
              layout="vertical"
              initialValues={{ timeRange: getDefaultTimeRange() }}
            >
              <Form.Item
                name="timeRange"
                label="选择账单生成的时间范围（基于发货时间）"
                rules={[{ required: true, message: "请选择时间范围" }]}
              >
                <RangePicker
                  showTime
                  style={{ width: "100%" }}
                  placeholder={["开始时间", "结束时间"]}
                  format="YYYY-MM-DD HH:mm:ss"
                  allowClear
                  presets={generateTimePresets()}
                />
              </Form.Item>
              <Form.Item>
                <Button
                  type="primary"
                  onClick={handleTimeSelection}
                  loading={loading}
                >
                  下一步
                </Button>
              </Form.Item>
            </Form>
          </Card>
        );
      }
      case GenerateStep.SELECT_USERS: {
        // 过滤用户列表
        const filteredUserList = userList.filter((user) => {
          if (!userSearchText.trim()) return true;
          const searchLower = userSearchText.toLowerCase();
          return (
            user.nickname.toLowerCase().includes(searchLower) ||
            user.username.toLowerCase().includes(searchLower) ||
            user.userId.toString().includes(searchLower)
          );
        });

        return (
          <Card
            title={`时间范围：${timeRange?.[0]} ~ ${timeRange?.[1]}`}
            extra={
              <Space>
                <Text>已选择 {selectedUserIds.length} 个用户</Text>
                <Button
                  icon={<CalendarOutlined />}
                  onClick={handleReselectTime}
                  type="default"
                >
                  重新选择时间
                </Button>
              </Space>
            }
          >
            <Space direction="vertical" size="middle" style={{ width: "100%" }}>
              {/* 用户检索 */}
              <div style={{ marginBottom: "16px" }}>
                <Input
                  placeholder="搜索用户（支持昵称、用户名、ID）"
                  value={userSearchText}
                  onChange={(e) => setUserSearchText(e.target.value)}
                  allowClear
                  style={{ width: "300px" }}
                  prefix={<SearchOutlined />}
                />
                <Text type="secondary" style={{ marginLeft: "12px" }}>
                  显示 {filteredUserList.length} / {userList.length} 个用户
                </Text>
              </div>

              {/* 批量操作 */}
              <div>
                <Checkbox
                  checked={
                    filteredUserList.length > 0 &&
                    filteredUserList.every((user) =>
                      selectedUserIds.includes(user.userId)
                    )
                  }
                  indeterminate={
                    filteredUserList.some((user) =>
                      selectedUserIds.includes(user.userId)
                    ) &&
                    !filteredUserList.every((user) =>
                      selectedUserIds.includes(user.userId)
                    )
                  }
                  onChange={(e) => {
                    if (e.target.checked) {
                      // 选中当前筛选结果中的所有用户
                      const newSelectedIds = [
                        ...selectedUserIds,
                        ...filteredUserList
                          .filter(
                            (user) => !selectedUserIds.includes(user.userId)
                          )
                          .map((user) => user.userId),
                      ];
                      setSelectedUserIds(newSelectedIds);
                    } else {
                      // 取消选中当前筛选结果中的所有用户
                      const filteredUserIds = filteredUserList.map(
                        (user) => user.userId
                      );
                      setSelectedUserIds(
                        selectedUserIds.filter(
                          (id) => !filteredUserIds.includes(id)
                        )
                      );
                    }
                  }}
                >
                  全选当前筛选结果
                </Checkbox>
                <Button
                  style={{ marginLeft: "16px" }}
                  onClick={() => setSelectedUserIds([])}
                  disabled={selectedUserIds.length === 0}
                >
                  清空选择
                </Button>
              </div>

              {/* 用户列表 */}
              <Table
                columns={userColumns}
                dataSource={filteredUserList}
                rowKey="userId"
                loading={loading}
                size="middle"
                pagination={{
                  showSizeChanger: false,
                  showQuickJumper: false,
                  pageSize: 10,
                  showTotal: (total, range) =>
                    `显示 ${range[0]}-${range[1]} 条记录，共 ${total} 个用户`,
                }}
              />

              {/* 操作按钮 */}
              <div style={{ textAlign: "right" }}>
                <Space>
                  <Button onClick={handleReselectTime}>上一步</Button>
                  <Button
                    type="primary"
                    onClick={handleUserSelection}
                    disabled={selectedUserIds.length === 0}
                    loading={loading}
                  >
                    下一步
                  </Button>
                </Space>
              </div>
            </Space>
          </Card>
        );
      }
      case GenerateStep.CONFIGURE_TEMPLATES: {
        // 过滤用户列表
        const filteredUserTemplatesState = userTemplatesState.filter(
          (userState) => {
            if (!customerSearchText.trim()) return true;
            const searchLower = customerSearchText.toLowerCase();
            return (
              userState.nickname.toLowerCase().includes(searchLower) ||
              userState.username.toLowerCase().includes(searchLower) ||
              userState.userId.toString().includes(searchLower)
            );
          }
        );

        return (
          <Card
            title="配置运费模板"
            extra={
              <Button
                icon={<UserOutlined />}
                onClick={handleReselectUsers}
                type="default"
              >
                重新选择用户
              </Button>
            }
          >
            <Form form={templateForm} layout="vertical">
              {/* 基本设置 */}
              <Card
                size="small"
                title="基本设置"
                style={{ marginBottom: "16px" }}
              >
                <Row gutter={16}>
                  <Col span={8}>
                    <Form.Item
                      name="dueDate"
                      label="付款截止日期"
                      rules={[
                        { required: true, message: "请选择付款截止日期" },
                      ]}
                      initialValue={dayjs().add(30, "day")}
                    >
                      <DatePicker style={{ width: "100%" }} />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item
                      name="currency"
                      label="货币单位"
                      rules={[{ required: true, message: "请选择货币单位" }]}
                      initialValue="CNY"
                    >
                      <Select>
                        <Select.Option value="CNY">人民币 (CNY)</Select.Option>
                        <Select.Option value="USD">美元 (USD)</Select.Option>
                        <Select.Option value="EUR">欧元 (EUR)</Select.Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item name="notes" label="账单备注">
                      <Input placeholder="请输入账单备注" />
                    </Form.Item>
                  </Col>
                </Row>
              </Card>

              {/* 操作按钮 - 移到中间位置 */}
              <div style={{ textAlign: "right", marginBottom: "16px" }}>
                <Space>
                  <Button onClick={handleReselectUsers}>上一步</Button>
                  <Button type="primary" onClick={handleTemplateConfiguration}>
                    下一步
                  </Button>
                </Space>
              </div>

              {/* 用户模板配置 */}
              <Card size="small" title="用户模板配置">
                {/* 客户检索 */}
                <div style={{ marginBottom: "16px" }}>
                  <Input
                    placeholder="搜索客户（支持昵称、用户名、ID）"
                    value={customerSearchText}
                    onChange={(e) => setCustomerSearchText(e.target.value)}
                    allowClear
                    style={{ width: "300px" }}
                  />
                  <Text type="secondary" style={{ marginLeft: "12px" }}>
                    显示 {filteredUserTemplatesState.length} /{" "}
                    {userTemplatesState.length} 个用户
                  </Text>
                </div>

                {/* 用户配置列表 - 设置固定高度和滚动 */}
                <div
                  style={{
                    maxHeight: "400px",
                    overflowY: "auto",
                    border: "1px solid #f0f0f0",
                    borderRadius: "6px",
                    padding: "12px",
                  }}
                >
                  <Space
                    direction="vertical"
                    size="middle"
                    style={{ width: "100%" }}
                  >
                    {filteredUserTemplatesState.map((userState) => (
                      <Card
                        key={userState.userId}
                        size="small"
                        title={
                          <div>
                            <span>{userState.nickname}</span>
                            <Text
                              type="secondary"
                              style={{ marginLeft: "8px" }}
                            >
                              (ID: {userState.userId} | 用户名:{" "}
                              {userState.username})
                            </Text>
                          </div>
                        }
                        extra={
                          <Space>
                            <Text type="secondary">
                              {userState.useSystemDefault
                                ? "使用系统默认"
                                : "自定义配置"}
                            </Text>
                            <Button
                              size="small"
                              icon={<SettingOutlined />}
                              loading={userState.loading}
                              onClick={() =>
                                toggleUserTemplateMode(
                                  userState.userId,
                                  !userState.useSystemDefault
                                )
                              }
                            >
                              {userState.useSystemDefault
                                ? "自定义配置"
                                : "使用默认"}
                            </Button>
                          </Space>
                        }
                      >
                        {!userState.useSystemDefault &&
                          userState.templateLoaded && (
                            <Collapse size="small">
                              {renderTemplateConfigForm(
                                userState,
                                "generalTemplate",
                                "普通货物模板"
                              )}
                              {renderTemplateConfigForm(
                                userState,
                                "batteryTemplate",
                                "带电货物模板"
                              )}
                              {renderTemplateConfigForm(
                                userState,
                                "postBoxTemplate",
                                "投函货物模板"
                              )}
                            </Collapse>
                          )}
                        {!userState.useSystemDefault &&
                          !userState.templateLoaded &&
                          !userState.loading && (
                            <Text type="secondary">
                              模板配置加载失败，请重新尝试自定义配置
                            </Text>
                          )}
                        {userState.useSystemDefault && (
                          <Text type="secondary">
                            将使用系统配置的默认模板参数
                          </Text>
                        )}
                      </Card>
                    ))}
                  </Space>
                </div>

                {/* 提示信息 */}
                <div
                  style={{ marginTop: "12px", color: "#666", fontSize: "12px" }}
                >
                  💡
                  提示：用户列表已设置滚动区域，操作按钮已移至上方便于快速访问
                </div>
              </Card>
            </Form>
          </Card>
        );
      }
      case GenerateStep.CONFIRM:
        return (
          <Card title="确认生成账单">
            <Space direction="vertical" size="middle" style={{ width: "100%" }}>
              {/* 基本信息 */}
              <div>
                <Title level={5}>基本信息</Title>
                <Row gutter={16}>
                  <Col span={12}>
                    <Text strong>时间范围：</Text>
                    <Text>
                      {timeRange?.[0]} ~ {timeRange?.[1]}
                    </Text>
                  </Col>
                  <Col span={12}>
                    <Text strong>用户数量：</Text>
                    <Text>{selectedUserIds.length} 个</Text>
                  </Col>
                  <Col span={12}>
                    <Text strong>货币单位：</Text>
                    <Text>
                      {templateForm.getFieldValue("currency") || "CNY"}
                    </Text>
                  </Col>
                  <Col span={12}>
                    <Text strong>付款截止日期：</Text>
                    <Text>
                      {templateForm
                        .getFieldValue("dueDate")
                        ?.format("YYYY-MM-DD") || "未设置"}
                    </Text>
                  </Col>
                </Row>
              </div>

              {/* 选中的用户 */}
              <div>
                <Title level={5}>选中的用户</Title>
                <div style={{ maxHeight: "200px", overflowY: "auto" }}>
                  {selectedUserIds.map((userId) => {
                    const user = userList.find((u) => u.userId === userId);
                    const userState = userTemplatesState.find(
                      (s) => s.userId === userId
                    );
                    return (
                      <Tag key={userId} style={{ margin: "4px" }}>
                        {user?.nickname} (ID: {userId})
                        {userState?.useSystemDefault
                          ? " - 默认模板"
                          : " - 自定义模板"}
                      </Tag>
                    );
                  })}
                </div>
              </div>

              {/* 模板配置摘要 */}
              <div>
                <Title level={5}>模板配置摘要</Title>
                <Text type="secondary">
                  系统默认模板：
                  {
                    userTemplatesState.filter((s) => s.useSystemDefault).length
                  }{" "}
                  个用户
                </Text>
                <br />
                <Text type="secondary">
                  自定义模板：
                  {
                    userTemplatesState.filter((s) => !s.useSystemDefault).length
                  }{" "}
                  个用户
                </Text>
              </div>

              {/* 操作按钮 */}
              <div style={{ textAlign: "right" }}>
                <Space>
                  <Button onClick={handleReconfigureTemplates}>上一步</Button>
                  <Button
                    type="primary"
                    onClick={handleConfirmGeneration}
                    loading={loading}
                    icon={<PlayCircleOutlined />}
                  >
                    开始生成账单
                  </Button>
                </Space>
              </div>
            </Space>
          </Card>
        );

      case GenerateStep.TASK_MONITORING:
        return renderTaskMonitoring();

      default:
        return null;
    }
  };

  /**
   * 处理关闭
   */
  const handleClose = () => {
    resetState();
    onClose();
  };

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  // 重置状态当弹窗显示状态改变时
  useEffect(() => {
    if (visible) {
      resetState();
    }
  }, [visible]);

  return (
    <Modal
      title="生成账单"
      open={visible}
      onCancel={handleClose}
      footer={null}
      width={1200}
      destroyOnClose
    >
      <Spin spinning={loading}>
        <Steps
          current={currentStep}
          items={steps}
          style={{ marginBottom: "24px" }}
        />
        {renderStepContent()}
      </Spin>
    </Modal>
  );
};

export default GenerateBillModal;
